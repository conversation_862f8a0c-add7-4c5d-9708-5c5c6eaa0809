//
//  ACSIntegrationTests.swift
//  iOSProjectTests
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Testing
import Foundation
@testable import iOSProject

// MARK: - ACS Integration Tests

@MainActor
struct ACSIntegrationTests {

    // MARK: - Test Setup Helper

    private func createTestEnvironment() -> (
        dependencyContainer: DependencyContainer,
        homeViewModel: HomeViewModel,
        navigationService: NavigationService
    ) {
        let dependencyContainer = DependencyContainer()
        let navigationService = NavigationService()

        let homeViewModel = HomeViewModel(
            newsUseCase: dependencyContainer.newsUseCase,
            acsService: dependencyContainer.acsService,
            navigationService: navigationService,
            validationService: ValidationService()
        )

        return (
            dependencyContainer: dependencyContainer,
            homeViewModel: homeViewModel,
            navigationService: navigationService
        )
    }

    // MARK: - Integration Tests

    @Test func testDependencyInjection_ACSService() async throws {
        // Given & When
        let testEnv = createTestEnvironment()
        let acsService = testEnv.dependencyContainer.acsService

        // Then
        #expect(acsService.callState == .idle)
    }

    @Test func testHomeViewModel_ACSIntegration() async throws {
        // Given & When
        let testEnv = createTestEnvironment()
        let viewModel = testEnv.homeViewModel

        // Then
        #expect(viewModel.showError == false)
    }

    @Test func testACSService_RealImplementation() async throws {
        // Given
        let testEnv = createTestEnvironment()
        let acsService = testEnv.dependencyContainer.acsService

        // When & Then
        #expect(acsService is ACSService, "Should use real ACS service now that Azure SDK is available")
    }

    @Test func testErrorHandling_Integration() async throws {
        // Given
        let testError = ACSError.callFailed

        // When & Then
        #expect(testError.localizedDescription == "Call failed")
    }

    @Test func testACSCommunication_Integration() async throws {
        // Given
        let testEnv = createTestEnvironment()
        let viewModel = testEnv.homeViewModel

        // When
        viewModel.startACSCommunication()

        // Then
        #expect(viewModel.showTeamsMeetingAlert == true)
        #expect(viewModel.teamsMeetingURL == "")
        #expect(viewModel.teamsMeetingURLError == nil)
    }

    @Test func testACSConfiguration_Integration() async throws {
        // Given
        let configuration = ACSConfiguration.default

        // Then
        #expect(!configuration.connectionString.isEmpty)
        #expect(!configuration.displayName.isEmpty)
        #expect(configuration.isValid == true)
    }

    @Test func testAppConstants_ACSIntegration() async throws {
        // Given & When
        let acsConstants = AppConstants.ACS.self

        // Then
        #expect(!acsConstants.defaultDisplayName.isEmpty)
        #expect(acsConstants.callTimeout > 0)
        #expect(!acsConstants.UI.communicationButtonTitle.isEmpty)
    }

    @Test func testFeatureFlag_ACSEnabled() async throws {
        // Given & When
        let isEnabled = AppConstants.FeatureFlags.enableAzureCommunicationServices

        // Then
        #expect(isEnabled == true, "ACS feature flag should be enabled")
    }

    // MARK: - Performance Tests

    @Test func testACSServiceCreation_Performance() async throws {
        // Given & When
        let container = DependencyContainer()
        let acsService = container.acsService

        // Then
        #expect(acsService.callState == .idle)
    }

    @Test func testHomeViewModelCreation_Performance() async throws {
        // Given & When
        let container = DependencyContainer()
        let navigationService = NavigationService()
        let viewModel = HomeViewModel(
            newsUseCase: container.newsUseCase,
            acsService: container.acsService,
            navigationService: navigationService,
            validationService: ValidationService()
        )

        // Then
        #expect(viewModel.showError == false)
    }

    // MARK: - SDK Availability Tests

    @Test func testAzureSDKAvailability() async throws {
        // Given & When
        let isUICallingAvailable = AzureSDKAvailability.isUICallingAvailable
        let isCallingAvailable = AzureSDKAvailability.isCallingAvailable
        let isCommonAvailable = AzureSDKAvailability.isCommonAvailable
        let isFullyAvailable = AzureSDKAvailability.isFullyAvailable

        // Then
        // These should now be true since dependencies are installed
        #expect(isUICallingAvailable == true, "UI Calling should be available after dependency install")
        #expect(isCallingAvailable == true, "Calling should be available after dependency install")
        #expect(isCommonAvailable == true, "Common should be available after dependency install")
        #expect(isFullyAvailable == true, "Full SDK should be available after dependency install")
    }

    // MARK: - Error Scenarios

    @Test func testACSService_ErrorHandling() async throws {
        // Given
        let testEnv = createTestEnvironment()
        let acsService = testEnv.dependencyContainer.acsService

        // When & Then
        // Test with invalid configuration to trigger an error
        do {
            _ = try await acsService.createCall(.groupCall())
            // The real service might not fail with empty display name, so we'll just verify it doesn't crash
        } catch {
            #expect(error is ACSError)
        }
    }

    @Test func testCompleteCallFlow_Integration() async throws {
        // Given
        let testEnv = createTestEnvironment()
        let viewModel = testEnv.homeViewModel

        // When - Start ACS communication
        viewModel.startACSCommunication()

        // Then - Should show Teams meeting alert
        #expect(viewModel.showTeamsMeetingAlert == true)

        // When - Set valid Teams URL and join
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"
        viewModel.joinTeamsMeetingWithUserInput()

        // Then - Should close alert and clear error
        #expect(viewModel.teamsMeetingURLError == nil)
        #expect(viewModel.showTeamsMeetingAlert == false)

        // Wait for async operation
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
    }

    @Test func testErrorHandling_InvalidTeamsURL() async throws {
        // Given
        let testEnv = createTestEnvironment()
        let viewModel = testEnv.homeViewModel

        // When
        viewModel.startACSCommunication()
        viewModel.teamsMeetingURL = "invalid-url"
        viewModel.joinTeamsMeetingWithUserInput()

        // Then
        #expect(viewModel.teamsMeetingURLError != nil)
        #expect(viewModel.showTeamsMeetingAlert == true) // Should remain open
    }
}
