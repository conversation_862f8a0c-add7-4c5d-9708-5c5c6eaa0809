//
//  HomeViewModelTests.swift
//  iOSProjectTests
//
//  Created by Code Quality Improvement on 12/08/2025.
//

import Testing
import Foundation
import Combine
@testable import iOSProject

// MARK: - Home ViewModel Tests

@MainActor
struct HomeViewModelTests {
    
    // MARK: - Test Setup Helper
    
    private func createViewModel() -> (
        viewModel: HomeViewModel,
        mocks: (
            newsUseCase: MockNewsUseCase,
            acsService: MockACSService,
            navigationService: MockNavigationService,
            validationService: ValidationService
        )
    ) {
        let mockNewsUseCase = MockNewsUseCase()
        let mockACSService = MockACSService()
        let mockNavigationService = MockNavigationService()
        let validationService = ValidationService()
        
        let viewModel = HomeViewModel(
            newsUseCase: mockNewsUseCase,
            acsService: mockACSService,
            navigationService: mockNavigationService,
            validationService: validationService
        )
        
        return (
            viewModel: viewModel,
            mocks: (
                newsUseCase: mockNewsUseCase,
                acsService: mockACSService,
                navigationService: mockNavigationService,
                validationService: validationService
            )
        )
    }
    
    // MARK: - Initialization Tests
    
    @Test func testInitialState() async throws {
        let (viewModel, _) = createViewModel()
        
        #expect(viewModel.isLoading == false)
        #expect(viewModel.showError == false)
        #expect(viewModel.errorMessage == AppConstants.Strings.genericError)
        #expect(viewModel.newsletterEmail == "")
        #expect(viewModel.isSubscribing == false)
        #expect(viewModel.showTeamsMeetingAlert == false)
        #expect(viewModel.teamsMeetingURL == "")
        #expect(viewModel.teamsMeetingURLError == nil)
    }
    
    @Test func testInitialDataLoading() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Wait for initial data loading to complete
        await TestUtilities.waitForAsyncOperation(timeout: 1.0)
        
        // Then
        #expect(viewModel.isLoading == false)
        #expect(viewModel.landingData.features.count == 4) // From LandingData.default
        #expect(viewModel.landingData.pricingPlans.count == 2)
        #expect(viewModel.landingData.partnerLogos.count == 6)
    }
    
    // MARK: - Data Loading Tests
    
    @Test func testLoadInitialDataSuccess() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.loadInitialData()
        
        // Then - should be loading immediately
        #expect(viewModel.isLoading == true)
        
        // Wait for completion
        await TestUtilities.waitForAsyncOperation(timeout: 1.0)
        
        // Then - should complete successfully
        #expect(viewModel.isLoading == false)
        #expect(viewModel.showError == false)
        #expect(viewModel.landingData.features.count > 0)
    }
    
    @Test func testRefreshData() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.refreshData()
        
        // Then - should trigger loading
        #expect(viewModel.isLoading == true)
        
        // Wait for completion
        await TestUtilities.waitForAsyncOperation(timeout: 1.0)
        
        // Then
        #expect(viewModel.isLoading == false)
    }
    
    // MARK: - User Interaction Tests
    
    @Test func testAppStoreButtonTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.appStoreButtonTapped()
        
        // Then - should not crash and log interaction
        #expect(true) // Placeholder - in real implementation, we'd verify logging
    }
    
    @Test func testPlayStoreButtonTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.playStoreButtonTapped()
        
        // Then - should not crash and log interaction
        #expect(true) // Placeholder
    }
    
    @Test func testGetStartedTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.getStartedTapped()
        
        // Then - should not crash and log interaction
        #expect(true) // Placeholder
    }
    
    // MARK: - Newsletter Subscription Tests
    
    @Test func testNewsletterSubscriptionWithValidEmail() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        viewModel.newsletterEmail = "<EMAIL>"
        
        // When
        viewModel.subscribeToNewsletter()
        
        // Then - should start subscribing
        #expect(viewModel.isSubscribing == true)
        
        // Wait for completion
        await TestUtilities.waitForAsyncOperation(timeout: 1.0)
        
        // Then - should complete and show success
        #expect(viewModel.isSubscribing == false)
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(viewModel.newsletterEmail == "") // Should clear email after success
    }
    
    @Test func testNewsletterSubscriptionWithInvalidEmail() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        viewModel.newsletterEmail = "invalid-email"
        
        // When
        viewModel.subscribeToNewsletter()
        
        // Then - should show error toast
        await TestUtilities.waitForAsyncOperation(timeout: 0.2)
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(mocks.navigationService.toasts.last?.type == .error)
    }
    
    @Test func testNewsletterSubscriptionWithEmptyEmail() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        viewModel.newsletterEmail = ""
        
        // When
        viewModel.subscribeToNewsletter()
        
        // Then - should show error toast
        await TestUtilities.waitForAsyncOperation(timeout: 0.2)
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(mocks.navigationService.toasts.last?.type == .error)
    }
    
    // MARK: - ACS Communication Tests
    
    @Test func testStartACSCommunication() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.startACSCommunication()
        
        // Then
        #expect(viewModel.teamsMeetingURL == "")
        #expect(viewModel.teamsMeetingURLError == nil)
        #expect(viewModel.showTeamsMeetingAlert == true)
    }
    
    @Test func testJoinTeamsMeetingWithValidURL() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"
        
        // When
        viewModel.joinTeamsMeetingWithUserInput()
        
        // Then
        #expect(viewModel.teamsMeetingURLError == nil)
        #expect(viewModel.showTeamsMeetingAlert == false)
        
        // Wait for ACS service call
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then - should have called ACS service
        #expect(mocks.acsService.joinCallCalled == true)
        #expect(mocks.acsService.lastCallType != nil)
    }
    
    @Test func testJoinTeamsMeetingWithInvalidURL() async throws {
        let (viewModel, _) = createViewModel()
        
        // Given
        viewModel.teamsMeetingURL = "invalid-url"
        
        // When
        viewModel.joinTeamsMeetingWithUserInput()
        
        // Then
        #expect(viewModel.teamsMeetingURLError != nil)
        #expect(viewModel.showTeamsMeetingAlert == true) // Should remain open
    }
    
    @Test func testCancelTeamsMeetingInput() async throws {
        let (viewModel, _) = createViewModel()
        
        // Given
        viewModel.showTeamsMeetingAlert = true
        viewModel.teamsMeetingURL = "some-url"
        viewModel.teamsMeetingURLError = "some error"
        
        // When
        viewModel.cancelTeamsMeetingInput()
        
        // Then
        #expect(viewModel.showTeamsMeetingAlert == false)
        #expect(viewModel.teamsMeetingURL == "")
        #expect(viewModel.teamsMeetingURLError == nil)
    }
    
    // MARK: - ACS Error Handling Tests

    @Test func testACSCommunicationError() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        mocks.acsService.simulateError(.callFailed)
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When
        viewModel.joinTeamsMeetingWithUserInput()

        // Wait for async operation
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)

        // Then - should handle error gracefully
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(mocks.navigationService.toasts.last?.type == .error)
    }

    @Test func testACSPermissionError() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        mocks.acsService.simulateError(.permissionDenied)
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When
        viewModel.joinTeamsMeetingWithUserInput()

        // Wait for async operation
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)

        // Then - should handle permission error
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(mocks.navigationService.toasts.last?.type == .error)
    }

    @Test func testACSNetworkError() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        mocks.acsService.simulateError(.networkError)
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When
        viewModel.joinTeamsMeetingWithUserInput()

        // Wait for async operation
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)

        // Then - should handle network error
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(mocks.navigationService.toasts.last?.type == .error)
    }

    @Test func testACSConfigurationError() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        mocks.acsService.simulateError(.configurationError)
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When
        viewModel.joinTeamsMeetingWithUserInput()

        // Wait for async operation
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)

        // Then - should handle configuration error
        #expect(mocks.navigationService.toasts.count > 0)
        #expect(mocks.navigationService.toasts.last?.type == .error)
    }
    
    // MARK: - Error Handling Tests

    @Test func testErrorStateInitialization() async throws {
        let (viewModel, _) = createViewModel()

        // Then - error state should be initialized correctly
        #expect(viewModel.showError == false)
        #expect(viewModel.errorMessage == AppConstants.Strings.genericError)
    }
    
    // MARK: - Call State Management Tests

    @Test func testCallStateTransitions() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When
        viewModel.joinTeamsMeetingWithUserInput()

        // Wait for state transitions
        await TestUtilities.waitForAsyncOperation(timeout: 0.3)

        // Then - should have gone through state transitions
        #expect(mocks.acsService.joinCallCalled == true)
        #expect(mocks.acsService.callState == .connected)
    }

    @Test func testCallHistoryTracking() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test1"

        // When - make first call
        viewModel.joinTeamsMeetingWithUserInput()
        await TestUtilities.waitForAsyncOperation(timeout: 0.2)

        // Reset and make second call
        mocks.acsService.reset()
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test2"
        viewModel.joinTeamsMeetingWithUserInput()
        await TestUtilities.waitForAsyncOperation(timeout: 0.2)

        // Then
        #expect(mocks.acsService.joinCallCalled == true)
        #expect(mocks.acsService.lastCallType != nil)
    }

    @Test func testPermissionHandling() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given - simulate permission denied initially
        mocks.acsService.simulatePermissionDenied()
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When
        viewModel.joinTeamsMeetingWithUserInput()
        await TestUtilities.waitForAsyncOperation(timeout: 0.3)

        // Then - should have attempted to request permissions
        #expect(mocks.acsService.checkPermissionsCalled == true)
    }

    // MARK: - State Management Tests

    @Test func testMultipleAsyncOperations() async throws {
        let (viewModel, _) = createViewModel()

        // When - trigger multiple operations
        viewModel.loadInitialData()
        viewModel.refreshData()

        // Then - should handle concurrent operations gracefully
        await TestUtilities.waitForAsyncOperation(timeout: 1.5)

        #expect(viewModel.isLoading == false)
        #expect(viewModel.showError == false)
    }

    @Test func testConcurrentACSOperations() async throws {
        let (viewModel, mocks) = createViewModel()

        // Given
        viewModel.teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/test"

        // When - trigger multiple ACS operations
        viewModel.joinTeamsMeetingWithUserInput()
        viewModel.startACSCommunication()

        // Then - should handle concurrent operations gracefully
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)

        #expect(mocks.acsService.joinCallCalled == true)
    }
}
