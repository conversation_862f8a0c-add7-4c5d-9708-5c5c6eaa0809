//
//  AuthenticationViewModelTests.swift
//  iOSProjectTests
//
//  Created by Code Quality Improvement on 12/08/2025.
//

import Testing
import Foundation
import Combine
@testable import iOSProject

// MARK: - Authentication ViewModel Tests

@MainActor
struct AuthenticationViewModelTests {
    
    // MARK: - Test Setup Helper
    
    private func createViewModel() -> (
        viewModel: AuthenticationViewModel,
        mocks: (
            authUseCase: MockAuthUseCase,
            validationUseCase: MockValidationUseCase,
            authNavigationUseCase: MockAuthNavigationUseCase
        )
    ) {
        let mockAuthUseCase = MockAuthUseCase()
        let mockValidationUseCase = MockValidationUseCase()
        let mockAuthNavigationUseCase = MockAuthNavigationUseCase()
        
        let viewModel = AuthenticationViewModel(
            authUseCase: mockAuthUseCase,
            validationUseCase: mockValidationUseCase,
            authNavigationUseCase: mockAuthNavigationUseCase
        )
        
        return (
            viewModel: viewModel,
            mocks: (
                authUseCase: mockAuthUseCase,
                validationUseCase: mockValidationUseCase,
                authNavigationUseCase: mockAuthNavigationUseCase
            )
        )
    }
    
    // MARK: - Initialization Tests
    
    @Test func testInitialState() async throws {
        let (viewModel, _) = createViewModel()
        
        #expect(viewModel.email == "")
        #expect(viewModel.password == "")
        #expect(viewModel.isPasswordVisible == false)
        #expect(viewModel.isLoading == false)
        #expect(viewModel.showError == false)
        #expect(viewModel.errorMessage == "")
        #expect(viewModel.emailError == nil)
        #expect(viewModel.passwordError == nil)
    }
    
    // MARK: - Login Tests
    
    @Test func testLoginWithValidCredentials() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.authUseCase.loginResult = .success(User.mock)
        mocks.validationUseCase.credentialsValidationResult = (.valid, .valid)
        viewModel.email = "<EMAIL>"
        viewModel.password = "validPassword123"
        
        // When
        viewModel.loginTapped()
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then
        #expect(mocks.authUseCase.loginCalled == true)
        #expect(mocks.authNavigationUseCase.navigateToMainCalled == true)
        #expect(mocks.authNavigationUseCase.showLoginSuccessCalled == true)
        #expect(viewModel.isLoading == false)
        #expect(viewModel.showError == false)
    }
    
    @Test func testLoginWithInvalidCredentials() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.authUseCase.loginResult = .failure(TestError.authenticationFailed)
        mocks.validationUseCase.credentialsValidationResult = (.valid, .valid)
        viewModel.email = "<EMAIL>"
        viewModel.password = "validPassword123"
        
        // When
        viewModel.loginTapped()
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then
        #expect(mocks.authUseCase.loginCalled == true)
        #expect(mocks.authNavigationUseCase.navigateToMainCalled == false)
        #expect(mocks.authNavigationUseCase.showLoginSuccessCalled == false)
        #expect(viewModel.isLoading == false)
        #expect(viewModel.showError == true)
        #expect(viewModel.errorMessage == "Authentication failed")
    }
    
    @Test func testLoginWithInvalidForm() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.validationUseCase.credentialsValidationResult = (
            .invalid("Invalid email"),
            .invalid("Password too short")
        )
        viewModel.email = "invalid-email"
        viewModel.password = "123"
        
        // When
        viewModel.loginTapped()
        await TestUtilities.waitForAsyncOperation(timeout: 0.2)
        
        // Then
        #expect(mocks.authUseCase.loginCalled == false)
        #expect(viewModel.emailError == "Invalid email")
        #expect(viewModel.passwordError == "Password too short")
    }
    
    // MARK: - Validation Tests
    
    @Test func testEmailValidation() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.validationUseCase.emailValidationResult = .invalid("Invalid email format")
        
        // When
        viewModel.email = "invalid-email"
        
        // Wait for debounced validation
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then
        #expect(viewModel.emailError == "Invalid email format")
    }
    
    @Test func testPasswordValidation() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.validationUseCase.passwordValidationResult = .invalid("Password too short")
        
        // When
        viewModel.password = "123"
        
        // Wait for debounced validation
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then
        #expect(viewModel.passwordError == "Password too short")
    }
    
    @Test func testValidEmailClearsError() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given - start with invalid email
        mocks.validationUseCase.emailValidationResult = .invalid("Invalid email")
        viewModel.email = "invalid"
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        #expect(viewModel.emailError == "Invalid email")
        
        // When - change to valid email
        mocks.validationUseCase.emailValidationResult = .valid
        viewModel.email = "<EMAIL>"
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then
        #expect(viewModel.emailError == nil)
    }
    
    // MARK: - UI State Tests
    
    @Test func testPasswordVisibilityToggle() async throws {
        let (viewModel, _) = createViewModel()
        
        // Given
        #expect(viewModel.isPasswordVisible == false)
        
        // When
        viewModel.togglePasswordVisibility()
        
        // Then
        #expect(viewModel.isPasswordVisible == true)
        
        // When
        viewModel.togglePasswordVisibility()
        
        // Then
        #expect(viewModel.isPasswordVisible == false)
    }
    
    @Test func testLoadingStateManagement() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.authUseCase.loginResult = .success(User.mock)
        mocks.validationUseCase.credentialsValidationResult = (.valid, .valid)
        viewModel.email = "<EMAIL>"
        viewModel.password = "validPassword123"
        
        // When
        viewModel.loginTapped()
        
        // Then - should be loading immediately
        #expect(viewModel.isLoading == true)
        
        // Wait for completion
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then - should not be loading after completion
        #expect(viewModel.isLoading == false)
    }
    
    // MARK: - Social Login Tests
    
    @Test func testGoogleLoginTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.googleLoginTapped()
        
        // Then - Currently just prints, but we can verify it doesn't crash
        #expect(true) // Placeholder assertion
    }
    
    @Test func testAppleLoginTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.appleLoginTapped()
        
        // Then - Currently just prints, but we can verify it doesn't crash
        #expect(true) // Placeholder assertion
    }
    
    @Test func testForgotPasswordTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.forgotPasswordTapped()
        
        // Then - Currently just prints, but we can verify it doesn't crash
        #expect(true) // Placeholder assertion
    }
    
    @Test func testSignUpTapped() async throws {
        let (viewModel, _) = createViewModel()
        
        // When
        viewModel.signUpTapped()
        
        // Then - Currently just prints, but we can verify it doesn't crash
        #expect(true) // Placeholder assertion
    }
    
    // MARK: - Edge Cases
    
    @Test func testEmptyCredentialsValidation() async throws {
        let (viewModel, mocks) = createViewModel()
        
        // Given
        mocks.validationUseCase.emailValidationResult = .valid
        mocks.validationUseCase.passwordValidationResult = .valid
        viewModel.email = ""
        viewModel.password = ""
        
        // When
        await TestUtilities.waitForAsyncOperation(timeout: 0.5)
        
        // Then - empty fields should not trigger validation errors
        #expect(viewModel.emailError == nil)
        #expect(viewModel.passwordError == nil)
    }
}
