//
//  TestMocks.swift
//  iOSProjectTests
//
//  Created by Code Quality Improvement on 12/08/2025.
//

import Foundation
import Combine
@testable import iOSProject

// MARK: - Mock Use Cases for Testing

class MockAuthUseCase: AuthUseCaseProtocol {
    var loginResult: Result<User, Error> = .success(User.mock)
    var loginCalled = false
    var logoutCalled = false
    var getCurrentUserResult: User? = User.mock
    
    func login(email: String, password: String) async throws -> User {
        loginCalled = true
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds for testing
        
        switch loginResult {
        case .success(let user):
            return user
        case .failure(let error):
            throw error
        }
    }
    
    func logout() async throws {
        logoutCalled = true
        try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds for testing
    }
    
    func getCurrentUser() -> User? {
        return getCurrentUserResult
    }
}

class MockValidationUseCase: ValidationUseCase {
    var emailValidationResult: ValidationResult = .valid
    var passwordValidationResult: ValidationResult = .valid
    var credentialsValidationResult: (email: ValidationResult, password: ValidationResult) = (.valid, .valid)
    
    func validateEmail(_ email: String) -> ValidationResult {
        return emailValidationResult
    }
    
    func validatePassword(_ password: String) -> ValidationResult {
        return passwordValidationResult
    }
    
    func validateCredentials(email: String, password: String) -> (email: ValidationResult, password: ValidationResult) {
        return credentialsValidationResult
    }
}

class MockAuthNavigationUseCase: AuthNavigationUseCase {
    var navigateToMainCalled = false
    var showLoginSuccessCalled = false
    var showLoginErrorCalled = false
    var lastErrorMessage: String?
    
    func navigateToMainAfterLogin() {
        navigateToMainCalled = true
    }
    
    func showLoginSuccess() {
        showLoginSuccessCalled = true
    }
    
    func showLoginError(_ message: String) {
        showLoginErrorCalled = true
        lastErrorMessage = message
    }
}

class MockNewsUseCase: NewsUseCaseProtocol {
    var getNewsResult: Result<[NewsItem], Error> = .success([])
    var getNewsItemResult: Result<NewsItem, Error> = .success(NewsItem.mock)
    var getNewsCalled = false
    var getNewsItemCalled = false
    var lastRequestedNewsId: String?
    
    func getNews() async throws -> [NewsItem] {
        getNewsCalled = true
        try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds for testing
        
        switch getNewsResult {
        case .success(let news):
            return news
        case .failure(let error):
            throw error
        }
    }
    
    func getNewsItem(id: String) async throws -> NewsItem {
        getNewsItemCalled = true
        lastRequestedNewsId = id
        try await Task.sleep(nanoseconds: 50_000_000) // 0.05 seconds for testing
        
        switch getNewsItemResult {
        case .success(let newsItem):
            return newsItem
        case .failure(let error):
            throw error
        }
    }
}

// Comprehensive mock for ACS service testing
class MockACSService: ObservableObject, ACSServiceProtocol {
    @Published var callState: ACSCallState = .idle
    var displayName: String = "Test User"

    // Call tracking
    var joinCallCalled = false
    var createCallCalled = false
    var endCallCalled = false
    var requestPermissionsCalled = false
    var checkPermissionsCalled = false
    var lastCallType: ACSServiceType?
    var callHistory: [ACSServiceType] = []

    // Error simulation
    var shouldThrowError = false
    var errorToThrow: Error = ACSError.configurationError
    var permissionGranted = true

    // State simulation
    var simulateStateTransitions = true
    var callDuration: TimeInterval = 0.1 // Simulated call duration

    // Return values
    var mockGroupId: String? = "mock-group-id"

    func joinCall(type: ACSServiceType) async throws -> String? {
        joinCallCalled = true
        lastCallType = type
        callHistory.append(type)

        if shouldThrowError {
            await MainActor.run {
                self.callState = .failed(errorToThrow as? ACSError ?? ACSError.callFailed)
            }
            throw errorToThrow
        }

        if simulateStateTransitions {
            await MainActor.run {
                self.callState = .connecting
            }
            try await Task.sleep(nanoseconds: UInt64(callDuration * 1_000_000_000))
            await MainActor.run {
                self.callState = .connected
            }
        }

        return mockGroupId
    }

    func createCall(_ type: ACSServiceType) async throws -> String? {
        createCallCalled = true
        lastCallType = type
        return try await joinCall(type: type)
    }

    func endCall() async {
        endCallCalled = true

        if simulateStateTransitions {
            await MainActor.run {
                self.callState = .disconnecting
            }
            try? await Task.sleep(nanoseconds: UInt64(callDuration * 500_000_000)) // Half duration for disconnect
        }

        await MainActor.run {
            self.callState = .disconnected
        }
    }

    func requestPermissions() async throws {
        requestPermissionsCalled = true

        if shouldThrowError {
            throw errorToThrow
        }

        // Simulate permission grant
        permissionGranted = true
    }

    func checkPermissions() async -> Bool {
        checkPermissionsCalled = true
        return permissionGranted && !shouldThrowError
    }

    // MARK: - Test Helper Methods

    func reset() {
        joinCallCalled = false
        createCallCalled = false
        endCallCalled = false
        requestPermissionsCalled = false
        checkPermissionsCalled = false
        lastCallType = nil
        callHistory.removeAll()
        shouldThrowError = false
        errorToThrow = ACSError.configurationError
        permissionGranted = true
        callState = .idle
    }

    func simulateError(_ error: ACSError) {
        shouldThrowError = true
        errorToThrow = error
    }

    func simulatePermissionDenied() {
        permissionGranted = false
    }
}

class MockNavigationService: NavigationServiceProtocol {
    var navigateToMainCalled = false
    var navigateToAuthCalled = false
    var navigateBackCalled = false
    var toasts: [Toast] = []
    var clearToastsCalled = false
    
    func navigateToMainNavigation() {
        navigateToMainCalled = true
    }
    
    func navigateToAuthentication() {
        navigateToAuthCalled = true
    }
    
    func navigateBack() {
        navigateBackCalled = true
    }
    
    func showToast(_ value: Toast) {
        toasts.append(value)
    }
    
    func clearToasts() {
        clearToastsCalled = true
        toasts.removeAll()
    }
}

// MARK: - Test Data Extensions

extension User {
    static let mock = User(
        id: "test_user_123",
        email: "<EMAIL>",
        name: "Test User",
        profileImageURL: nil,
        isEmailVerified: true
    )
    
    static let mockUnverified = User(
        id: "test_user_456",
        email: "<EMAIL>",
        name: "Unverified User",
        profileImageURL: nil,
        isEmailVerified: false
    )
}

extension NewsItem {
    static let mock = NewsItem(
        id: "test_news_1",
        title: "Test News Article",
        description: "This is a test news article description",
        content: "This is the full content of the test news article...",
        imageURL: nil,
        author: "Test Author",
        publishedAt: Date(),
        category: .technology
    )
    
    static let mockList: [NewsItem] = [
        NewsItem(
            id: "test_news_1",
            title: "First Test Article",
            description: "First test description",
            content: "First test content",
            author: "Author 1",
            category: .technology
        ),
        NewsItem(
            id: "test_news_2",
            title: "Second Test Article",
            description: "Second test description",
            content: "Second test content",
            author: "Author 2",
            category: .business
        )
    ]
}

// MARK: - Test Validation Results

extension ValidationResult {
    static let invalidEmail = ValidationResult.invalid("Invalid email format")
    static let invalidPassword = ValidationResult.invalid("Password too short")
    static let emptyField = ValidationResult.invalid("Field is required")
}

// MARK: - Test Errors

enum TestError: Error, LocalizedError {
    case networkFailure
    case authenticationFailed
    case dataCorrupted
    
    var errorDescription: String? {
        switch self {
        case .networkFailure:
            return "Network connection failed"
        case .authenticationFailed:
            return "Authentication failed"
        case .dataCorrupted:
            return "Data is corrupted"
        }
    }
}

// MARK: - Test Utilities

class TestUtilities {
    static func createMockDependencyContainer() -> (
        authUseCase: MockAuthUseCase,
        validationUseCase: MockValidationUseCase,
        authNavigationUseCase: MockAuthNavigationUseCase,
        newsUseCase: MockNewsUseCase,
        acsService: MockACSService,
        navigationService: MockNavigationService
    ) {
        return (
            authUseCase: MockAuthUseCase(),
            validationUseCase: MockValidationUseCase(),
            authNavigationUseCase: MockAuthNavigationUseCase(),
            newsUseCase: MockNewsUseCase(),
            acsService: MockACSService(),
            navigationService: MockNavigationService()
        )
    }
    
    static func waitForAsyncOperation(timeout: TimeInterval = 1.0) async {
        try? await Task.sleep(nanoseconds: UInt64(timeout * 1_000_000_000))
    }
}
