import Foundation

// MARK: - Validation Result
enum ValidationResult {
    case valid
    case invalid(String)
    
    var isValid: Bool {
        switch self {
        case .valid:
            return true
        case .invalid:
            return false
        }
    }
    
    var errorMessage: String? {
        switch self {
        case .valid:
            return nil
        case .invalid(let message):
            return message
        }
    }
}

// MARK: - Validation UseCase Protocol (Domain Layer)
protocol ValidationUseCase {
    func validateEmail(_ email: String) -> ValidationResult
    func validatePassword(_ password: String) -> ValidationResult
    func validateCredentials(email: String, password: String) -> (email: ValidationResult, password: ValidationResult)
}

// MARK: - Validation Service Protocol (Infrastructure Layer)
protocol ValidationServiceProtocol {
    func validateEmail(_ email: String) -> ValidationResult
    func validatePassword(_ password: String) -> ValidationResult
    func validateTeamsMeetingURL(_ url: String) -> ValidationResult
}
