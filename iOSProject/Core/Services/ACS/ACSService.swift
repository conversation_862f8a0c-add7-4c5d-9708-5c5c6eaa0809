//
//  ACSService.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation
import SwiftUI
import AVFoundation
import AzureCommunicationUICalling
import AzureCommunicationCalling
import AzureCommunicationCommon
import os


class CustomThemeOptions: ThemeOptions {
   var primaryColor: UIColor {
       return UIColor(Color.appMain)
   }
    var colorSchemeOverride: UIUserInterfaceStyle {  UITraitCollection.current.userInterfaceStyle  }
}

// MARK: - ACS Service Protocol
protocol ACSServiceProtocol: ObservableObject {
    var callState: ACSCallState { get }
    var displayName: String { get set }

    func joinCall(type: ACSServiceType) async throws -> String?
    func createCall(_ type: ACSServiceType) async throws -> String?
    func endCall() async
    func requestPermissions() async throws
    func checkPermissions() async -> Bool
}

// MARK: - ACS Service Implementation

/// Simplified Azure Communication Services implementation
///
/// This service always launches the real Azure Communication Services SDK interface,
/// allowing developers to see and test the actual SDK user experience.
/// Even with placeholder credentials, the SDK will attempt to launch and handle
/// authentication errors naturally, showing the real ACS calling interface.
class ACSService: ObservableObject, ACSServiceProtocol {

    // MARK: - Properties

    @Published private(set) var callState: ACSCallState = .idle
    private var localOptions: LocalOptions?

    private var callComposite: CallComposite?
    private let connectionString: String
    private let userAccessToken: String
     var displayName: String
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "iOSProject", category: "ACS")

    // MARK: - Performance Tracking
    private var connectionStartTime: Date?
    private var lastCleanupTime: Date = Date()

    /// Check if we're using development/placeholder credentials
    private var isDevelopmentMode: Bool {
        return connectionString.contains("your-acs-resource") || connectionString.contains("your-access-key")
    }

    // MARK: - Initialization

    init(connectionString: String, displayName: String, userAccessToken:String) {
        self.connectionString = connectionString
        self.displayName = displayName
        self.userAccessToken = userAccessToken
    }

    /// Convenience initializer using default configuration
    convenience init() {
        let config = ACSConfiguration.fromCustomPlist
        self.init(connectionString: config.connectionString, displayName: config.displayName, userAccessToken: config.userAccessToken)
    }

    /// Cleanup resources when the service is deallocated
    deinit {
        logger.debug("ACSService deallocating - cleaning up resources")

        // Ensure call composite is properly dismissed
        callComposite?.dismiss()
        callComposite = nil

        logger.debug("ACSService cleanup completed")
    }

    // MARK: - Error Handling & Retry Logic

    /// Performs an operation with retry logic
    private func performWithRetry<T>(
        maxAttempts: Int = 3,
        delay: TimeInterval = 1.0,
        operation: @escaping () async throws -> T
    ) async throws -> T {
        var lastError: Error?

        for attempt in 1...maxAttempts {
            do {
                return try await operation()
            } catch {
                lastError = error
                logger.warning("Attempt \(attempt) failed: \(error.localizedDescription)")

                if attempt < maxAttempts {
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        throw lastError ?? ACSError.callFailed
    }

    /// Handles ACS errors and updates state accordingly
    private func handleACSError(_ error: Error) async {
        let acsError: ACSError

        if let existingACSError = error as? ACSError {
            acsError = existingACSError
        } else {
            // Map other errors to ACS errors
            switch error {
            case is URLError:
                acsError = .networkError
            default:
                acsError = .callFailed
            }
        }

        await MainActor.run {
            self.callState = .failed(acsError)
        }

        logger.error("ACS Error: \(acsError.localizedDescription)")
    }

    // MARK: - Public Methods

    /// Join a call using a Teams meeting URL or group call ID
    func joinCall(type: ACSServiceType) async throws -> String? {
        connectionStartTime = Date()
        logger.info("Starting call join process")

        do {
            // Validate permissions first
            let hasPermissions = await checkPermissions()
            if !hasPermissions {
                logger.warning("Permissions not granted, requesting permissions")
                try await requestPermissions()
            }

            await MainActor.run { callState = .connecting }

            // Create call composite with retry logic
            let composite = try await performWithRetry(maxAttempts: 3) { [self] in
                try await self.createCallComposite()
            }
            // Determine locator type based on URL format
            let locator: JoinLocator
            var returnGroupId: UUID? = nil
            switch type {
            case let .groupCall(groupID):
               
                let groupID = groupID ?? UUID()
                returnGroupId = groupID
                locator = .groupCall(groupId: groupID)
                logger.info("Joining group call")
           
            case .teamsMeeting(link: let link):
              
                locator = .teamsMeeting(teamsLink: link)
                logger.info("Joining team meeting with link")
            
            case let .teamsMeetingId(meetingId, meetingPasscode):
               
                locator = .teamsMeetingId(meetingId: meetingId, meetingPasscode: meetingPasscode)
                logger.info("Joining team meeting with meeting id & meeting passcode")
            
            case let .roomCall(roomId):
              
                locator = .roomCall(roomId: roomId)
                logger.info("Joining room call with room id")
            }
            // Launch the call - this will show the real ACS SDK interface
            await MainActor.run {
                if let opts = self.localOptions {
                    composite.launch(locator: locator, localOptions: opts)
                } else {
                    composite.launch(locator: locator)
                }
                callState = .connected

                // Log connection performance
                if let startTime = connectionStartTime {
                    let connectionTime = Date().timeIntervalSince(startTime)
                    logger.info("Call connected successfully in \(String(format: "%.2f", connectionTime)) seconds")
                }
            }

            return returnGroupId?.uuidString
            
        } catch {
            await handleACSError(error)
            throw error
        }
    }
    
    
    
    /// Create a call
    func createCall(_ type: ACSServiceType) async throws -> String? {
        logger.info("Creating call")
        do {
            return try await joinCall(type: type)
        } catch {
            logger.error("Failed to create call: \(error.localizedDescription)")
            throw error
        }
    }

    /// End the current call
    func endCall() async {
        logger.info("Ending call")

        await MainActor.run {
            callState = .disconnecting
        }

        await MainActor.run {
            // Properly cleanup call composite
            callComposite?.dismiss()
            callComposite = nil
            callState = .disconnected

            // Reset performance tracking
            connectionStartTime = nil
        }

        // Perform periodic cleanup
        await performPeriodicCleanup()

        logger.info("Call ended successfully")
    }

    /// Perform periodic cleanup to manage memory efficiently
    private func performPeriodicCleanup() async {
        let now = Date()
        let timeSinceLastCleanup = now.timeIntervalSince(lastCleanupTime)

        // Perform cleanup every 5 minutes
        if timeSinceLastCleanup > 300 {
            logger.debug("Performing periodic cleanup")

            // Force garbage collection hint (iOS will decide if needed)
            await MainActor.run {
                // Clear any cached data that's no longer needed
                lastCleanupTime = now
            }

            logger.debug("Periodic cleanup completed")
        }
    }

    /// Check if camera and microphone permissions are granted
    func checkPermissions() async -> Bool {
        let cameraStatus = await checkCameraPermission()
        let microphoneStatus = await checkMicrophonePermission()
        return cameraStatus && microphoneStatus
    }
    
    /// Request camera and microphone permissions
    func requestPermissions() async throws {
        let cameraGranted = await requestCameraPermission()
        let microphoneGranted = await requestMicrophonePermission()

        if !cameraGranted || !microphoneGranted {
            throw ACSError.permissionDenied
        }
    }
    
    // MARK: - Private Methods
    private func createCallComposite() async throws -> CallComposite {
        do {
            let token = try await generateToken()
            
            // Attempt to create credential; if token is invalid, this will throw
            let credential: CommunicationTokenCredential
            do {
                credential = try CommunicationTokenCredential(token: token)
            } catch {
                logger.error("Invalid ACS token: \(error.localizedDescription)")
                throw ACSError.configurationError
            }
            
            let callCompositeOptions = CallCompositeOptions(theme: CustomThemeOptions(), displayName: displayName)
            let composite = CallComposite(credential: credential, withOptions: callCompositeOptions)
            
            // Configure setup screen title/subtitle (use initializer for LocalOptions)
            let setupData = SetupScreenViewData(
                title: "Group Call",
                subtitle: displayName
            )
            let opts = LocalOptions(
                participantViewData: nil,
                setupScreenViewData: setupData,
                cameraOn: nil,
                microphoneOn: nil,
                skipSetupScreen: nil,
                audioVideoMode: .audioAndVideo,
                captionsOptions: nil,
                setupScreenOptions: nil,
                callScreenOptions: AzureCommunicationUICalling.CallScreenOptions()
            )
            self.localOptions = opts
            
            setupEventHandlers(for: composite)
            self.callComposite = composite
            return composite
            
        } catch {
            logger.error("CallComposite creation failed: \(error.localizedDescription)")
            await MainActor.run {
                callState = .failed(ACSError.configurationError)
            }
            throw error
        }
    }
// This is the part when should call backend functionality using api for dynamic token generation
    private func generateToken() async throws -> String {
        // This is a simplified token generation for development
        // In production, should call  backend to generate a proper token
        return userAccessToken
    }
    
    private func setupEventHandlers(for composite: CallComposite) {
        // Handle call state changes
        composite.events.onCallStateChanged = { [weak self] callStateEvent in
            DispatchQueue.main.async {
                self?.handleCallStateChange(callStateEvent)
            }
        }

        // Handle errors - log them but let the SDK display its own error UI
        composite.events.onError = { [weak self] error in
            DispatchQueue.main.async {
                self?.logger.error("ACS SDK error: \(error.error?.localizedDescription ?? "Unknown error")")
                // Don't override the call state - let the SDK handle the error display
                // The SDK will show its own error messages and UI
            }
        }

        // Handle dismissal
        composite.events.onDismissed = { [weak self] _ in
            DispatchQueue.main.async {
                self?.callState = .disconnected
                self?.callComposite = nil
                self?.logger.info("ACS SDK interface dismissed")
            }
        }
    }
    
    private func handleCallStateChange(_ state: AzureCommunicationUICalling.CallState) {
        // Simplified state mapping
        switch state {
        case .connecting:
            callState = .connecting
        case .connected:
            callState = .connected
        case .disconnecting:
            callState = .disconnecting
        case .disconnected:
            callState = .disconnected
        default:
            break
        }
    }
    
    private func checkCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        return status == .authorized
    }

    private func checkMicrophonePermission() async -> Bool {
        let status = AVAudioSession.sharedInstance().recordPermission
        return status == .granted
    }

    private func requestCameraPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                continuation.resume(returning: granted)
            }
        }
    }

    private func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }
}
