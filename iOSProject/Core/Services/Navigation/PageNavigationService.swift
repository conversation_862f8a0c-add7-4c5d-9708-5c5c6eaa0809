//
//  PageNavigationService.swift
//  iOSProject
//
//  Created by Code Quality Improvement on 12/08/2025.
//

import SwiftUI
import Combine
import OSLog

// MARK: - Page Navigation Service Protocol

protocol PageNavigationServiceProtocol: ObservableObject {
    var currentView: AppView { get set }
    var navigationPath: NavigationPath { get set }
    var isDrawerOpen: Bool { get set }
    var locale: String { get }
    
    func navigateToMainNavigation()
    func navigateToAuthentication()
    func pushView(_ destination: NavigationDestination)
    func popView()
    func popToRoot()
    func updateLocale(_ locale: String)
    func toggleDrawer()
}

// MARK: - Page Navigation Service

class PageNavigationService: PageNavigationServiceProtocol {
    // MARK: - Published Properties
    @Published var currentView: AppView = .authentication
    @Published var navigationPath = NavigationPath()
    @Published var isDrawerOpen: Bool = false
    @Published private(set) var locale: String = "en"

    // MARK: - Private Storage
    @AppStorage(.locale) private var storedLocale: String = "en"
    
    // MARK: - Private Properties
    private let logger = Logger.navigation
    
    // MARK: - Initialization
    init() {
        // Sync published locale with stored value
        self.locale = storedLocale
        logger.info("PageNavigationService initialized with locale: \(self.locale)")
    }
    
    // MARK: - Public Methods
    func navigateToMainNavigation() {
        logger.info("Navigating to main navigation")
        currentView = .mainNavigation
        navigationPath = NavigationPath()
    }
    
    func navigateToAuthentication() {
        logger.info("Navigating to authentication")
        currentView = .authentication
        navigationPath = NavigationPath()
    }
    
    func pushView(_ destination: NavigationDestination) {
        logger.info("Pushing view: \(String(describing: destination))")
        navigationPath.append(destination)
    }
    
    func popView() {
        logger.info("Popping view")
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
    }
    
    func popToRoot() {
        logger.info("Popping to root")
        navigationPath = NavigationPath()
    }
    
    func updateLocale(_ locale: String) {
        logger.info("Updating locale to: \(locale)")
        self.storedLocale = locale  // Update AppStorage
        self.locale = locale        // Update Published property
    }
    
    func toggleDrawer() {
        logger.info("Toggling drawer")
        isDrawerOpen.toggle()
    }
    
    // MARK: - Profile Navigation
    func navigateToEditProfile() {
        pushView(.editProfile)
    }
    
    func navigateToAddress() {
        pushView(.address)
    }
    
    func navigateToHistory() {
        pushView(.history)
    }
    
    func navigateToComplain() {
        pushView(.complain)
    }
    
    func navigateToPrivacyPolicy() {
        pushView(.privacyPolicy)
    }
    
    func navigateToHelpSupport() {
        pushView(.helpSupport)
    }
    
    // MARK: - Content Navigation
    func navigateToNewsDetail(_ newsId: String) {
        pushView(.newsDetail(newsId))
    }
    
    func navigateToNotificationDetail(_ notificationId: String) {
        pushView(.notificationDetail(notificationId))
    }
}

// MARK: - App View
enum AppView {
    case authentication
    case mainNavigation
}

// MARK: - Navigation Destination
enum NavigationDestination: Hashable {
    case editProfile
    case address
    case history
    case complain
    case privacyPolicy
    case helpSupport
    case referral
    case aboutUs
    case settings
    case newsDetail(String)
    case notificationDetail(String)
}

// MARK: - Navigation View Builder
extension NavigationDestination: View {
    var body: some View {
        switch self {
        case .editProfile:
            EditProfileView()
        case .address:
            AddressView()
        case .history:
            HistoryView()
        case .complain:
            ComplainView()
        case .privacyPolicy:
            PrivacyPolicyView()
        case .helpSupport:
            HelpSupportView()
        case .referral:
            ReferralView()
        case .aboutUs:
            AboutUsView()
        case .settings:
            SettingsView()
        case .newsDetail(let newsId):
            NewsDetailView(newsId: newsId)
        case .notificationDetail(let notificationId):
            NotificationDetailView(notificationId: notificationId)
        }
    }
}

// MARK: - Placeholder Views
struct AddressView: View {
    var body: some View {
        Text("Address")
            .navigationTitle("Address")
    }
}

struct HistoryView: View {
    var body: some View {
        Text("History")
            .navigationTitle("History")
    }
}

struct ComplainView: View {
    var body: some View {
        Text("Complain")
            .navigationTitle("Complain")
    }
}

struct PrivacyPolicyView: View {
    var body: some View {
        Text("Privacy Policy")
            .navigationTitle("Privacy Policy")
    }
}

struct HelpSupportView: View {
    var body: some View {
        Text("Help & Support")
            .navigationTitle("Help & Support")
    }
}

struct NewsDetailView: View {
    let newsId: String
    
    var body: some View {
        Text("News Detail: \(newsId)")
            .navigationTitle("News Detail")
    }
}

struct NotificationDetailView: View {
    let notificationId: String

    var body: some View {
        Text("Notification Detail: \(notificationId)")
            .navigationTitle("Notification Detail")
    }
}

struct ReferralView: View {
    var body: some View {
        Text("Referral")
            .navigationTitle("Referral")
    }
}

struct AboutUsView: View {
    var body: some View {
        Text("About Us")
            .navigationTitle("About Us")
    }
}

struct SettingsView: View {
    var body: some View {
        Text("Settings")
            .navigationTitle("Settings")
    }
}
