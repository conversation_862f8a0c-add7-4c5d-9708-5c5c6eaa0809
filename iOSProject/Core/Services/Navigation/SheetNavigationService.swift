//
//  SheetNavigationService.swift
//  iOSProject
//
//  Created by Code Quality Improvement on 12/08/2025.
//

import SwiftUI
import Combine
import OSLog

// MARK: - Sheet Navigation Service Protocol

protocol SheetNavigationServiceProtocol: ObservableObject {
    var presentedSheet: SheetType? { get set }
    var showAlert: Bool { get set }
    var alertMessage: String { get set }
    
    func presentSheet(_ sheet: SheetType)
    func dismissSheet()
    func showAlert(message: String)
    func dismissAlert()
}

// MARK: - Sheet Navigation Service

class SheetNavigationService: SheetNavigationServiceProtocol {
    // MARK: - Published Properties
    @Published var presentedSheet: SheetType?
    @Published var showAlert = false
    @Published var alertMessage = ""
    
    // MARK: - Private Properties
    private let logger = Logger.navigation
    
    // MARK: - Initialization
    init() {
        logger.info("SheetNavigationService initialized")
    }
    
    // MARK: - Public Methods
    func presentSheet(_ sheet: SheetType) {
        logger.info("Presenting sheet: \(sheet.id)")
        presentedSheet = sheet
    }
    
    func dismissSheet() {
        logger.info("Dismissing sheet")
        presentedSheet = nil
    }
    
    func showAlert(message: String) {
        logger.info("Showing alert: \(message)")
        alertMessage = message
        showAlert = true
    }
    
    func dismissAlert() {
        logger.info("Dismissing alert")
        showAlert = false
        alertMessage = ""
    }
}

// MARK: - Sheet Type
enum SheetType: Identifiable {
    case editProfile
    case imageEditor
    case languageSelector
    case themeSelector
    
    var id: String {
        switch self {
        case .editProfile: return "editProfile"
        case .imageEditor: return "imageEditor"
        case .languageSelector: return "languageSelector"
        case .themeSelector: return "themeSelector"
        }
    }
}

// MARK: - Sheet View Builder
extension SheetType: View {
    var body: some View {
        switch self {
        case .editProfile:
            EditProfileView()
        case .imageEditor:
            ImageEditorView()
        case .languageSelector:
            LanguageSelectorView()
        case .themeSelector:
            ThemeSelectorView()
        }
    }
}

// MARK: - Placeholder Views
struct EditProfileView: View {
    var body: some View {
        NavigationView {
            Text("Edit Profile")
                .navigationTitle("Edit Profile")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct ImageEditorView: View {
    var body: some View {
        NavigationView {
            Text("Image Editor")
                .navigationTitle("Image Editor")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct LanguageSelectorView: View {
    var body: some View {
        NavigationView {
            Text("Language Selector")
                .navigationTitle("Language")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct ThemeSelectorView: View {
    var body: some View {
        NavigationView {
            Text("Theme Selector")
                .navigationTitle("Theme")
                .navigationBarTitleDisplayMode(.inline)
        }
    }
}
