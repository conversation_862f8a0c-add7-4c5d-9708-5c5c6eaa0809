//
//  TabNavigationService.swift
//  iOSProject
//
//  Created by Code Quality Improvement on 12/08/2025.
//

import SwiftUI
import Combine
import OSLog

// MARK: - Tab Navigation Service Protocol

protocol TabNavigationServiceProtocol: ObservableObject {
    var selectedTab: Tab { get set }
    func selectTab(_ tab: Tab)
}

// MARK: - Tab Navigation Service

class TabNavigationService: TabNavigationServiceProtocol {
    // MARK: - Published Properties
    @Published var selectedTab: Tab = .home
    
    // MARK: - Private Properties
    private let logger = Logger.navigation
    
    // MARK: - Initialization
    init() {
        logger.info("TabNavigationService initialized")
    }
    
    // MARK: - Public Methods
    func selectTab(_ tab: Tab) {
        guard selectedTab != tab else { return }
        
        logger.info("Navigating to tab: \(tab.rawValue)")
        selectedTab = tab
    }
}

// MARK: - Tab enum is imported from TabModel.swift
// Using the existing Tab enum from Presentation/Dashboard/Model/TabModel.swift
