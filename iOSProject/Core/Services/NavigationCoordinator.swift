import SwiftUI
import Combine
import OSLog

// MARK: - Navigation Coordinator
class NavigationCoordinator: ObservableObject {
    // MARK: - Service Dependencies
    private let pageNavigationService: PageNavigationService
    private let tabNavigationService: TabNavigationService
    private let sheetNavigationService: SheetNavigationService

    // MARK: - Published Properties (Delegated to Services)
    @Published var currentView: AppView = .authentication
    @Published var selectedTab: Tab = .home
    @Published var isDrawerOpen: Bool = false
    @Published var navigationPath = NavigationPath()
    @Published var presentedSheet: SheetType?
    @Published var showAlert = false
    @Published var alertMessage = ""
    @Published var locale: String = "en"

    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let logger = Logger.navigation

    // MARK: - Initialization
    init(
        pageNavigationService: PageNavigationService = PageNavigationService(),
        tabNavigationService: TabNavigationService = TabNavigationService(),
        sheetNavigationService: SheetNavigationService = SheetNavigationService()
    ) {
        self.pageNavigationService = pageNavigationService
        self.tabNavigationService = tabNavigationService
        self.sheetNavigationService = sheetNavigationService

        setupBindings()
        logger.info("NavigationCoordinator initialized with service composition")
    }

    // MARK: - Setup
    private func setupBindings() {
        // Bind page navigation service
        pageNavigationService.$currentView
            .assign(to: &$currentView)

        pageNavigationService.$navigationPath
            .assign(to: &$navigationPath)

        pageNavigationService.$isDrawerOpen
            .assign(to: &$isDrawerOpen)

        // Bind locale changes from PageNavigationService
        pageNavigationService.$locale
            .assign(to: &$locale)

        // Bind tab navigation service
        tabNavigationService.$selectedTab
            .assign(to: &$selectedTab)

        // Bind sheet navigation service
        sheetNavigationService.$presentedSheet
            .assign(to: &$presentedSheet)

        sheetNavigationService.$showAlert
            .assign(to: &$showAlert)

        sheetNavigationService.$alertMessage
            .assign(to: &$alertMessage)
    }
    
    // MARK: - Navigation Methods (Delegated to Services)
    func navigateToMainNavigation() {
        pageNavigationService.navigateToMainNavigation()
    }

    func navigateToAuthentication() {
        pageNavigationService.navigateToAuthentication()
    }

    func navigateBack() {
        pageNavigationService.popView()
    }

    func pushView(_ view: NavigationDestination) {
        pageNavigationService.pushView(view)
    }

    func presentSheet(_ sheet: SheetType) {
        sheetNavigationService.presentSheet(sheet)
    }

    func dismissSheet() {
        sheetNavigationService.dismissSheet()
    }

    func showAlert(message: String) {
        sheetNavigationService.showAlert(message: message)
    }
    
    // MARK: - Tab Navigation (Delegated to Services)
    func selectTab(_ tab: Tab) {
        tabNavigationService.selectTab(tab)
    }

    func toggleDrawer() {
        pageNavigationService.toggleDrawer()
    }


    func updateLocale(_ locale: String) {
        pageNavigationService.updateLocale(locale)
    }

    // MARK: - Profile Navigation (Delegated to Services)
    func navigateToEditProfile() {
        pageNavigationService.navigateToEditProfile()
    }

    func navigateToAddress() {
        pageNavigationService.navigateToAddress()
    }

    func navigateToHistory() {
        pageNavigationService.navigateToHistory()
    }

    func navigateToComplain() {
        pageNavigationService.navigateToComplain()
    }

    func navigateToPrivacyPolicy() {
        pageNavigationService.navigateToPrivacyPolicy()
    }

    func navigateToHelpSupport() {
        pageNavigationService.navigateToHelpSupport()
    }

    func navigateToReferral() {
        pushView(.referral)
    }

    func navigateToAboutUs() {
        pushView(.aboutUs)
    }

    func navigateToSettings() {
        pushView(.settings)
    }
    
    // MARK: - Content Navigation (Delegated to Services)
    func navigateToNewsDetail(_ newsId: String) {
        pageNavigationService.navigateToNewsDetail(newsId)
    }

    func navigateToNotificationDetail(_ notificationId: String) {
        pageNavigationService.navigateToNotificationDetail(notificationId)
    }
}

// MARK: - Navigation Coordinator Summary
// This coordinator now delegates to focused services:
// - PageNavigationService: Handles main app navigation and routing
// - TabNavigationService: Manages tab selection
// - SheetNavigationService: Handles modal presentations and alerts
//
// The coordinator maintains backward compatibility while providing
// a cleaner, more maintainable architecture with separated concerns.
