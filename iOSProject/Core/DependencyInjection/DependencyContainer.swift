import SwiftUI
import Foundation

// MARK: - Dependency Container
class DependencyContainer: ObservableObject {

    private let diContainer: AppDIContainer

    init() {
        self.diContainer = AppDIContainer.configure()
    }

    // MARK: - Repositories
    var authRepository: AuthRepositoryProtocol {
        return diContainer.resolve(AuthRepositoryProtocol.self)
    }

    var userRepository: UserRepositoryProtocol {
        return diContainer.resolve(UserRepositoryProtocol.self)
    }

    var newsRepository: NewsRepositoryProtocol {
        return diContainer.resolve(NewsRepositoryProtocol.self)
    }

    var notificationRepository: NotificationRepositoryProtocol {
        return diContainer.resolve(NotificationRepositoryProtocol.self)
    }

    // MARK: - Use Cases
    var authUseCase: AuthUseCaseProtocol {
        return diContainer.resolve(AuthUseCaseProtocol.self)
    }

    var userUseCase: UserUseCaseProtocol {
        return diContainer.resolve(UserUseCaseProtocol.self)
    }

    var newsUseCase: NewsUseCaseProtocol {
        return diContainer.resolve(NewsUseCaseProtocol.self)
    }

    var notificationUseCase: NotificationUseCaseProtocol {
        return diContainer.resolve(NotificationUseCaseProtocol.self)
    }

    // MARK: - Infrastructure Services
    var validationService: ValidationServiceProtocol {
        return diContainer.resolve(ValidationServiceProtocol.self)
    }

    var navigationService: NavigationServiceProtocol {
        return diContainer.resolve(NavigationServiceProtocol.self)
    }

    var navigationCoordinator: NavigationCoordinator {
        return diContainer.resolve(NavigationCoordinator.self)
    }

    var acsService: ACSServiceProtocol {
        return diContainer.resolve(ACSServiceProtocol.self)
    }

    // MARK: - Domain Use Cases (Abstractions)
    var validationUseCase: ValidationUseCase {
        return diContainer.resolve(ValidationUseCase.self)
    }

    var authNavigationUseCase: AuthNavigationUseCase {
        return diContainer.resolve(AuthNavigationUseCase.self)
    }
}

// MARK: - Dependency Container Environment Key
struct DependencyContainerEnvironmentKey: EnvironmentKey {
    static let defaultValue = DependencyContainer()
}

extension EnvironmentValues {
    var dependencyContainer: DependencyContainer {
        get { self[DependencyContainerEnvironmentKey.self] }
        set { self[DependencyContainerEnvironmentKey.self] = newValue }
    }
}

// MARK: - View Extensions for Dependency Injection
extension View {
    func inject(_ container: DependencyContainer) -> some View {
        self.environmentObject(container)
    }
}


