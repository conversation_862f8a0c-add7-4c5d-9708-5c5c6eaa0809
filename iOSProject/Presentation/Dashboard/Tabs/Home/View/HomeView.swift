import SwiftUI


extension HomeView {
    static func build(container: DependencyContainer) -> some View {
      
        
        let acsService = container.acsService
        acsService.displayName = container.authUseCase.getCurrentUser()?.name ?? ""
        
      return  HomeView(viewModel: HomeViewModel(
            newsUseCase: container.newsUseCase,
            acsService: acsService,
            navigationService: container.navigationService,
            validationService: container.validationService
        ))
    }
}

// MARK: - Landing View

struct HomeView: View {
    @StateObject private var viewModel: HomeViewModel
    @Environment(\.appColors) var colors
    
    init(viewModel: HomeViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        MainScrollBody {
            LazyVStack(spacing: 0) {
                // Header Section
                HomeHeaderSection(
                    onACSButtonTapped: viewModel.startACSCommunication,
                    callState: viewModel.acsCallState,
                    isConnectingToCall: viewModel.isConnectingToCall,
                    isRequestingPermissions: viewModel.isRequestingPermissions
                )


                // Hero Section
                HomeHeroSection(onGetStarted: viewModel.getStartedTapped)

                // Partners Section
                HomePartnersSection()

                // Features Section
                HomeFeaturesSection(features: viewModel.landingData.features)

                // CTA Section
                HomeCTASection(
                    onAppStoreButtonTapped: viewModel.appStoreButtonTapped,
                    onPlayStoreButtonTapped: viewModel.playStoreButtonTapped
                )

                // Divider
                dividerSection

                // Pricing Section
                HomePricingSection(pricingPlans: viewModel.landingData.pricingPlans, onGetStarted: viewModel.getStartedTapped)

                // Divider
                dividerSection

                // Newsletter Section
                HomeNewsletterSection(
                    email: $viewModel.newsletterEmail,
                    isSubscribing: viewModel.isSubscribing,
                    onSubscribe: { viewModel.subscribeToNewsletter() }
                )
            }
        }
        .background(colors.background)
        .refreshable {
            viewModel.refreshData()
        }
        .overlay {
            if viewModel.isLoading {
                LoadingOverlay()
            }
        }
        .alert(AppConstants.Strings.error, isPresented: $viewModel.showError) {
            Button(AppConstants.Strings.ok) {}
        } message: {
            Text(viewModel.errorMessage)
        }
        .alert("Join Teams Meeting", isPresented: $viewModel.showTeamsMeetingAlert) {
            TextField("meeting.url.placeholder", text: $viewModel.teamsMeetingURL, prompt:  Text("https://teams.microsoft.com/l/meetup-join/...").foregroundColor(.gray))
                .textInputAutocapitalization(.never)
                .keyboardType(.URL)
                .autocorrectionDisabled()

            Button("Cancel", role: .cancel) {
                viewModel.cancelTeamsMeetingInput()
            }

            Button("Join Meeting") {
                viewModel.joinTeamsMeetingWithUserInput()
            }
            .disabled(viewModel.teamsMeetingURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
        } message: {
            VStack(alignment: .leading, spacing: 8) {
                if let error = viewModel.teamsMeetingURLError {
                    Text(error)
                        .foregroundColor(.red)
                } else {
                    Text("Enter a Microsoft Teams meeting URL to join the call.")
                    Text("Example: https://teams.microsoft.com/l/meetup-join/...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }

    // MARK: - Divider Section

    private var dividerSection: some View {
        Rectangle()
            .fill(colors.borderDefault)
            .frame(height: 1)
            .padding(.horizontal, 16.h)
    }



}



// MARK: - Preview
#Preview {
    NavigationStack{
        HomeView.build(container: DependencyContainer())
            .provideTheme(dark: false)
            .attachAllEnvironmentObjects()
    }
}
