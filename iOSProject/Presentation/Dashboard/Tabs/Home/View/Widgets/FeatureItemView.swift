import SwiftUI

// MARK: - Feature Item View
struct FeatureItemView: View {
    let feature: FeatureItem
    @Environment(\.appColors) var colors
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Icon Container
            iconContainer

            Spacer().frame(height: 16.h)

            // Title
            Text(feature.title)
                .headline18Medium()
                .themedTextColor(.primary)

            Spacer().frame(height: 8.h)

            // Description
            Text(feature.description)
                .title16Regular()
                .themedTextColor(.secondary)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Icon Container 
    private var iconContainer: some View {
        RoundedRectangle(cornerRadius: 20.h)
            .fill(Color(hex: "F4EBFF").opacity(0.1))
            .frame(width: 40.h, height: 40.h)
           
            .overlay(
                Image(feature.icon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .padding(6)
                    .frame(width: 24.h, height: 24.h)
                    .background(Color(hex: "F4EBFF").clipShape(.circle))
                    .foregroundColor(.primary) // Ensure icon color is visible
                   
                    
            )
    }
}

// MARK: - Preview
#Preview{

        VStack(spacing: 32) {
            FeatureItemView(
                feature: FeatureItem(
                    icon: ImageConstants.imgPiechart,
                    title: "Access to daily analytics",
                    description: "Optimize the way you recover, train, and sleep with daily reporting on mobile and desktop apps."
                )
            )

            FeatureItemView(
                feature: FeatureItem(
                    icon: ImageConstants.imgZap,
                    title: "Measure recovery",
                    description: "The most advanced sleep tracking technology available today."
                )
            )
        }
        .padding()
        .attachAllEnvironmentObjects()
    
}
