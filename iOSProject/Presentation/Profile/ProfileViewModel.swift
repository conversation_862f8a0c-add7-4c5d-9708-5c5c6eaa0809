import SwiftUI
import Combine

// MARK: - Profile View Model
@MainActor
class ProfileViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var user: UserProfile = UserProfile.defaultUser
    @Published var isLoading = false
    @Published var showError = false
    @Published var errorMessage = ""
    @Published private(set) var isEnglish: Bool = false
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard
    private let navigationCoordinator: NavigationCoordinator

    // MARK: - Keys
    private enum Keys {
        static let language = AppConstants.UserDefaults.selectedLanguage
        static let userProfile = AppConstants.UserDefaults.userProfile
    }

    // MARK: - Initialization
    init(navigationCoordinator: NavigationCoordinator) {
        self.navigationCoordinator = navigationCoordinator
        loadUserProfile()
        loadLanguagePreference()
        setupLocaleObserver()
    }
    
  

    private func setupLocaleObserver() {
        // Observe locale changes from NavigationCoordinator
        navigationCoordinator.$locale
            .map { $0 == "en" }
            .assign(to: &$isEnglish)
    }
    
    // MARK: - User Profile Management
    func loadUserProfile() {
        isLoading = true

        // Simulate loading from local storage or API
        Task { [weak self] in
            guard let self = self else { return }
            do {
                try await Task.sleep(nanoseconds: AppConstants.Delays.medium)

                await MainActor.run {
                    if let data = self.userDefaults.data(forKey: Keys.userProfile) {
                        do {
                            let profile = try JSONDecoder().decode(UserProfile.self, from: data)
                            self.user = profile
                        } catch {
                            // If decoding fails, use default user and show error
                            self.user = UserProfile.defaultUser
                            self.showErrorMessage("Failed to load saved profile, using defaults")
                        }
                    } else {
                        self.user = UserProfile.defaultUser
                    }
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.showErrorMessage("Failed to load profile: \(error.localizedDescription)")
                }
            }
        }
    }
    
    func updateUserProfile(_ profile: UserProfile) {
        user = profile
        saveUserProfile()
    }
    
    private func saveUserProfile() {
        do {
            let data = try JSONEncoder().encode(user)
            userDefaults.set(data, forKey: Keys.userProfile)
        } catch {
            showErrorMessage("Failed to save profile: \(error.localizedDescription)")
        }
    }

    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
    
  
    
    func loadLanguagePreference(){
        self.isEnglish = navigationCoordinator.locale == "en"
    }
    
    func changeLanguage(to language: String, navigationCoordinator:NavigationCoordinator) {
        // Remove optimistic update - let the reactive binding handle UI updates
        Task { [weak self] in
            guard let self = self else { return }
            try await Task.sleep(nanoseconds: AppConstants.Delays.long)
            await MainActor.run {
                
                withAnimation(.bouncy) {
                    navigationCoordinator.updateLocale(language)
                }
               
            }
        }
    }
    
    // MARK: - Profile Actions
    func editProfileImage() {
        // Handle profile image editing
        print("Edit profile image tapped")
    }
    
    func navigateToEditProfile() {
        navigationCoordinator.navigateToEditProfile()
    }

    func navigateToAddress() {
        navigationCoordinator.navigateToAddress()
    }

    func navigateToHistory() {
        navigationCoordinator.navigateToHistory()
    }

    func navigateToComplain() {
        navigationCoordinator.navigateToComplain()
    }

    func navigateToReferral() {
        navigationCoordinator.navigateToReferral()
    }

    func navigateToAboutUs() {
        navigationCoordinator.navigateToAboutUs()
    }

    func navigateToSettings() {
        navigationCoordinator.navigateToSettings()
    }

    func navigateToHelpSupport() {
        navigationCoordinator.navigateToHelpSupport()
    }
    
    func logout() {
        // Handle logout
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            // Clear user data
            self.userDefaults.removeObject(forKey: Keys.userProfile)
            
            // Navigate to authentication
            NotificationCenter.default.post(name: .userLoggedOut, object: nil)
            
            self.isLoading = false
        }
    }
}

// MARK: - User Profile Model
struct UserProfile: Codable, Equatable {
    let id: String
    var name: String
    var email: String
    var profileImageURL: String?
    var walletBalance: String
    
    static let defaultUser = UserProfile(
        id: "OP8761",
        name: "John Doe",
        email: "<EMAIL>",
        profileImageURL: nil,
        walletBalance: "50.00"
    )
}

// MARK: - Notification Names
extension Notification.Name {
    static let languageChanged = Notification.Name("languageChanged")
    static let userLoggedOut = Notification.Name("userLoggedOut")
    static let themeChanged = Notification.Name("themeChanged")
}

// MARK: - Profile Menu Item Model
struct ProfileMenuItem {
    let id = UUID()
    let icon: String
    let title: String
    let isLogout: Bool
    let action: () -> Void
    
    init(icon: String, title: String, isLogout: Bool = false, action: @escaping () -> Void) {
        self.icon = icon
        self.title = title
        self.isLogout = isLogout
        self.action = action
    }
}

// MARK: - Profile Menu Items Factory
extension ProfileViewModel {
    func createMenuItems() -> [ProfileMenuItem] {
        [
            ProfileMenuItem(icon: ImageConstants.imgUser, title: "Edit Profile") {
                self.navigateToEditProfile()
            },
            ProfileMenuItem(icon: ImageConstants.imgSearchGray900_0c, title: "Address") {
                self.navigateToAddress()
            },
            ProfileMenuItem(icon: ImageConstants.imgHistory, title: "History") {
                self.navigateToHistory()
            },
            ProfileMenuItem(icon: ImageConstants.imgComplain, title: "Complain") {
                self.navigateToComplain()
            },
            ProfileMenuItem(icon: ImageConstants.imgReferral, title: "Referral") {
                self.navigateToReferral()
            },
            ProfileMenuItem(icon: ImageConstants.imgAboutUs, title: "About Us") {
                self.navigateToAboutUs()
            },
            ProfileMenuItem(icon: ImageConstants.imgSettings, title: "Settings") {
                self.navigateToSettings()
            },
            ProfileMenuItem(icon: ImageConstants.imgHelpAndSupport, title: "Help & Support") {
                self.navigateToHelpSupport()
            },
            ProfileMenuItem(icon: ImageConstants.imgLogout, title: "Logout", isLogout: true) {
                self.logout()
            }
        ]
    }
}

