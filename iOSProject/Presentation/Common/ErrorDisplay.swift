//
//  ErrorDisplay.swift
//  iOSProject
//
//  Created by ACS Integration on 12/08/2025.
//

import SwiftUI

/// A comprehensive error display component with user-friendly messaging and recovery options
struct ErrorDisplay: View {
    @Environment(\.appColors) var colors
    
    let error: Error
    let onRetry: (() -> Void)?
    let onDismiss: (() -> Void)?
    
    init(
        error: Error,
        onRetry: (() -> Void)? = nil,
        onDismiss: (() -> Void)? = nil
    ) {
        self.error = error
        self.onRetry = onRetry
        self.onDismiss = onDismiss
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Error Icon
            Image(systemName: errorIcon)
                .font(.system(size: 48))
                .foregroundColor(errorColor)
            
            // Error Title
            Text(errorTitle)
                .font(.headline)
                .foregroundColor(colors.primaryText)
                .multilineTextAlignment(.center)
            
            // Error Message
            Text(errorMessage)
                .font(.body)
                .foregroundColor(colors.secondaryText)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            // Recovery Suggestions
            if !recoverySuggestions.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Try these steps:")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(colors.primaryText)
                    
                    ForEach(recoverySuggestions, id: \.self) { suggestion in
                        HStack(alignment: .top, spacing: 8) {
                            Text("•")
                                .foregroundColor(colors.secondaryText)
                            Text(suggestion)
                                .font(.caption)
                                .foregroundColor(colors.secondaryText)
                        }
                    }
                }
                .padding()
                .background(colors.surface)
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }
            
            // Action Buttons
            HStack(spacing: 12) {
                if let onDismiss = onDismiss {
                    Button("Dismiss") {
                        onDismiss()
                    }
                    .buttonStyle(SecondaryButtonStyle())
                }
                
                if let onRetry = onRetry {
                    Button("Try Again") {
                        onRetry()
                    }
                    .buttonStyle(PrimaryButtonStyle())
                }
            }
        }
        .padding(24)
        .background(colors.background)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
    
    // MARK: - Computed Properties
    
    private var errorIcon: String {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return "lock.shield"
            case .networkError:
                return "wifi.exclamationmark"
            case .callFailed:
                return "phone.down"
            case .configurationError:
                return "gear.badge.xmark"
            }
        }
        return "exclamationmark.triangle"
    }
    
    private var errorColor: Color {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return .orange
            case .networkError:
                return .blue
            case .callFailed, .configurationError:
                return .red
            }
        }
        return .red
    }
    
    private var errorTitle: String {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return "Permissions Required"
            case .networkError:
                return "Connection Problem"
            case .callFailed:
                return "Call Failed"
            case .configurationError:
                return "Configuration Error"
            }
        }
        return "Something Went Wrong"
    }
    
    private var errorMessage: String {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return "Camera and microphone access is required to join video calls. Please enable permissions in Settings."
            case .networkError:
                return "Unable to connect to the service. Please check your internet connection and try again."
            case .callFailed:
                return "Unable to join the call. The meeting URL might be invalid or the meeting may have ended."
            case .configurationError:
                return "There's a configuration issue with the communication service. Please contact support if this persists."
            }
        }
        return error.localizedDescription
    }
    
    private var recoverySuggestions: [String] {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return [
                    "Go to Settings > Privacy & Security > Camera",
                    "Enable camera access for this app",
                    "Go to Settings > Privacy & Security > Microphone",
                    "Enable microphone access for this app"
                ]
            case .networkError:
                return [
                    "Check your Wi-Fi or cellular connection",
                    "Try switching between Wi-Fi and cellular data",
                    "Restart your network connection"
                ]
            case .callFailed:
                return [
                    "Verify the Teams meeting URL is correct",
                    "Check if the meeting is still active",
                    "Try copying the URL again from the meeting invitation"
                ]
            case .configurationError:
                return [
                    "Restart the app",
                    "Check for app updates",
                    "Contact support if the issue persists"
                ]
            }
        }
        return []
    }
}

// MARK: - Button Styles

struct PrimaryButtonStyle: ButtonStyle {
    @Environment(\.appColors) var colors
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.body)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(colors.primaryPurple)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    @Environment(\.appColors) var colors
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.body)
            .fontWeight(.medium)
            .foregroundColor(colors.primaryText)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(colors.surface)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(colors.borderDefault, lineWidth: 1)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Inline Error Display

/// A compact error display for inline use
struct InlineErrorDisplay: View {
    @Environment(\.appColors) var colors
    
    let error: Error
    let onRetry: (() -> Void)?
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
                .font(.caption)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Error")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(colors.primaryText)
                
                Text(errorMessage)
                    .font(.caption2)
                    .foregroundColor(colors.secondaryText)
                    .lineLimit(2)
            }
            
            Spacer()
            
            if let onRetry = onRetry {
                Button("Retry") {
                    onRetry()
                }
                .font(.caption)
                .foregroundColor(colors.primaryPurple)
            }
        }
        .padding(12)
        .background(Color.red.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    private var errorMessage: String {
        if let acsError = error as? ACSError {
            switch acsError {
            case .permissionDenied:
                return "Permissions required"
            case .networkError:
                return "Connection failed"
            case .callFailed:
                return "Call failed"
            case .configurationError:
                return "Configuration error"
            }
        }
        return "Something went wrong"
    }
}

// MARK: - Preview
#Preview("Error Display") {
    VStack(spacing: 32) {
        ErrorDisplay(
            error: ACSError.permissionDenied,
            onRetry: { print("Retry tapped") },
            onDismiss: { print("Dismiss tapped") }
        )
        
        InlineErrorDisplay(
            error: ACSError.networkError,
            onRetry: { print("Retry tapped") }
        )
    }
    .padding()
    .attachAllEnvironmentObjects()
}
