//
//  CallStatusIndicator.swift
//  iOSProject
//
//  Created by ACS Integration on 12/08/2025.
//

import SwiftUI

/// A reusable SwiftUI component that displays the current ACS call state
/// with appropriate visual indicators, colors, and animations
struct CallStatusIndicator: View {
    @Environment(\.appColors) var colors
    
    // MARK: - Properties
    let state: ACSCallState
    let showText: Bool
    let size: CallStatusSize
    
    // MARK: - Initialization
    init(
        state: ACSCallState,
        showText: Bool = true,
        size: CallStatusSize = .medium
    ) {
        self.state = state
        self.showText = showText
        self.size = size
    }
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: size.spacing) {
            statusIcon
            
            if showText {
                Text(statusText)
                    .font(size.font)
                    .foregroundColor(statusColor)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: state)
    }
    
    // MARK: - Status Icon
    @ViewBuilder
    private var statusIcon: some View {
        Group {
            switch state {
            case .idle:
                Image(systemName: "phone")
                    .foregroundColor(colors.secondaryText)
                
            case .connecting:
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: colors.primaryPurple))
                    .scaleEffect(size.progressScale)
                
            case .connected:
                Image(systemName: "phone.fill")
                    .foregroundColor(.green)
                
            case .disconnecting:
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: colors.secondaryText))
                    .scaleEffect(size.progressScale)
                
            case .disconnected:
                Image(systemName: "phone.down")
                    .foregroundColor(colors.secondaryText)
                
            case .failed(let error):
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
            }
        }
        .frame(width: size.iconSize, height: size.iconSize)
    }
    
    // MARK: - Status Text
    private var statusText: String {
        switch state {
        case .idle:
            return "Ready to call"
        case .connecting:
            return "Connecting..."
        case .connected:
            return "Connected"
        case .disconnecting:
            return "Ending call..."
        case .disconnected:
            return "Call ended"
        case .failed(let error):
            return userFriendlyErrorMessage(for: error)
        }
    }
    
    // MARK: - Status Color
    private var statusColor: Color {
        switch state {
        case .idle, .disconnected:
            return colors.secondaryText
        case .connecting, .disconnecting:
            return colors.primaryPurple
        case .connected:
            return .green
        case .failed:
            return .red
        }
    }
    
    // MARK: - Error Message Helper
    private func userFriendlyErrorMessage(for error: ACSError) -> String {
        switch error {
        case .permissionDenied:
            return "Permission required"
        case .networkError:
            return "Connection failed"
        case .callFailed:
            return "Call failed"
        case .configurationError:
            return "Configuration error"
        }
    }
}

// MARK: - Call Status Size Configuration
enum CallStatusSize {
    case small
    case medium
    case large
    
    var iconSize: CGFloat {
        switch self {
        case .small: return 12
        case .medium: return 16
        case .large: return 20
        }
    }
    
    var spacing: CGFloat {
        switch self {
        case .small: return 4
        case .medium: return 6
        case .large: return 8
        }
    }
    
    var font: Font {
        switch self {
        case .small: return .caption
        case .medium: return .body
        case .large: return .headline
        }
    }
    
    var progressScale: CGFloat {
        switch self {
        case .small: return 0.6
        case .medium: return 0.8
        case .large: return 1.0
        }
    }
}

// MARK: - Call Status Card
/// A card-style wrapper for the call status indicator
struct CallStatusCard: View {
    @Environment(\.appColors) var colors
    
    let state: ACSCallState
    let onTap: (() -> Void)?
    
    init(state: ACSCallState, onTap: (() -> Void)? = nil) {
        self.state = state
        self.onTap = onTap
    }
    
    var body: some View {
        Button(action: { onTap?() }) {
            HStack {
                CallStatusIndicator(state: state, size: .medium)
                Spacer()
                
                if case .connected = state {
                    Image(systemName: "chevron.right")
                        .foregroundColor(colors.arrowIcon)
                        .font(.caption)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(colors.surface)
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(borderColor, lineWidth: 1)
            )
        }
        .disabled(onTap == nil)
        .buttonStyle(PlainButtonStyle())
    }
    
    private var borderColor: Color {
        switch state {
        case .connected:
            return .green.opacity(0.3)
        case .failed:
            return .red.opacity(0.3)
        default:
            return colors.borderDefault
        }
    }
}

// MARK: - Preview
#Preview("Call Status Indicator") {
    VStack(spacing: 16) {
        CallStatusIndicator(state: .idle)
        CallStatusIndicator(state: .connecting)
        CallStatusIndicator(state: .connected)
        CallStatusIndicator(state: .disconnecting)
        CallStatusIndicator(state: .disconnected)
        CallStatusIndicator(state: .failed(.callFailed))
        
        Divider()
        
        // Different sizes
        HStack {
            CallStatusIndicator(state: .connected, size: .small)
            CallStatusIndicator(state: .connected, size: .medium)
            CallStatusIndicator(state: .connected, size: .large)
        }
        
        Divider()
        
        // Card style
        CallStatusCard(state: .connected) {
            print("Status card tapped")
        }
    }
    .padding()
    .attachAllEnvironmentObjects()
}
