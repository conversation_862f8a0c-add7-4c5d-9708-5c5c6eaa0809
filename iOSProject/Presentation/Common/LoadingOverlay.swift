//
//  LoadingOverlay.swift
//  iOSProject
//
//  Created by ACS Integration on 12/08/2025.
//

import SwiftUI

/// A reusable loading overlay component that can be used throughout the app
struct LoadingOverlay: View {
    @Environment(\.appColors) var colors
    
    let message: String
    let showBackground: Bool
    
    init(message: String = "Loading...", showBackground: Bool = true) {
        self.message = message
        self.showBackground = showBackground
    }
    
    var body: some View {
        ZStack {
            if showBackground {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
            }
            
            VStack(spacing: 16) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: colors.primaryPurple))
                    .scaleEffect(1.2)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(colors.primaryText)
            }
            .padding(24)
            .background(colors.surface)
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
        .animation(.easeInOut(duration: 0.3), value: showBackground)
    }
}

// MARK: - Specialized Loading Overlays

/// Loading overlay specifically for ACS operations
struct ACSLoadingOverlay: View {
    @Environment(\.appColors) var colors
    
    let operation: ACSOperation
    
    var body: some View {
        LoadingOverlay(message: operation.message)
    }
}

enum ACSOperation {
    case requestingPermissions
    case connecting
    case joiningCall
    case endingCall
    
    var message: String {
        switch self {
        case .requestingPermissions:
            return "Requesting permissions..."
        case .connecting:
            return "Connecting to call..."
        case .joiningCall:
            return "Joining meeting..."
        case .endingCall:
            return "Ending call..."
        }
    }
}

// MARK: - Inline Loading Indicator

/// A smaller loading indicator for inline use
struct InlineLoadingIndicator: View {
    @Environment(\.appColors) var colors
    
    let message: String
    let size: LoadingSize
    
    init(message: String = "Loading...", size: LoadingSize = .medium) {
        self.message = message
        self.size = size
    }
    
    var body: some View {
        HStack(spacing: size.spacing) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: colors.primaryPurple))
                .scaleEffect(size.scale)
            
            Text(message)
                .font(size.font)
                .foregroundColor(colors.secondaryText)
        }
    }
}

enum LoadingSize {
    case small
    case medium
    case large
    
    var spacing: CGFloat {
        switch self {
        case .small: return 6
        case .medium: return 8
        case .large: return 12
        }
    }
    
    var scale: CGFloat {
        switch self {
        case .small: return 0.7
        case .medium: return 1.0
        case .large: return 1.3
        }
    }
    
    var font: Font {
        switch self {
        case .small: return .caption
        case .medium: return .body
        case .large: return .headline
        }
    }
}

// MARK: - Preview
#Preview("Loading Overlay") {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        VStack(spacing: 32) {
            LoadingOverlay(message: "Loading content...")
            
            ACSLoadingOverlay(operation: .connecting)
            
            InlineLoadingIndicator(message: "Syncing...", size: .small)
            InlineLoadingIndicator(message: "Processing...", size: .medium)
            InlineLoadingIndicator(message: "Uploading...", size: .large)
        }
    }
    .attachAllEnvironmentObjects()
}
