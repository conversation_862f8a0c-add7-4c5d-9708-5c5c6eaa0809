//
//  Modifiers.swift
//  iOSProject
//
//  Created by Apple on 10/08/2025.
//

import SwiftUI

struct EnvironmentModifier: ViewModifier {
    @StateObject private var themeManager = ThemeManager()
    @StateObject private var dependencyContainer = DependencyContainer()
    @StateObject private var navigationCoordinator = NavigationCoordinator()
    func body(content: Content) -> some View {
        
        
//        let navigationCoordinator: NavigationCoordinator = dependencyContainer.navigationCoordinator
        let navigationService: NavigationService = dependencyContainer.navigationService as! NavigationService
        content
            .environmentObject(dependencyContainer)
            .environmentObject(navigationCoordinator)
            .environmentObject(navigationService)
            .environmentObject(themeManager)
            .environment(\.locale, .init(identifier: navigationCoordinator.locale))
            .environment(\.layoutDirection, navigationCoordinator.locale.direction)
    }
}
